import { Auth } from "aws-amplify";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import InputField from "../../components/common/InputField";
import { reactQueryKeys } from "../../utils/constant";
import { useState } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { LoginFromSchemaType, loginFromSchema } from "../../models/login.model";
import styles from "./Login.module.scss";
import Loader from "../../components/common/Loader";
import { ReactComponent as ShowPass } from '../../../assests/images/show-pass.svg';
import { ReactComponent as HidePass } from '../../../assests/images/hide-pass.svg';

const Login = () => {
  const [callInProgress, setCallInProgress] = useState(false);
  const query = useQueryClient();
  const [showPassword, setShowPassword] = useState(false)

  const {
    register,
    control,
    handleSubmit,
    setError,
    reset,
    watch,
    setValue,
    formState: { errors },
  } = useForm<LoginFromSchemaType>({
    resolver: yupResolver(loginFromSchema),
  });

  const loginFormSubmitHandler = async (data: LoginFromSchemaType) => {
    reset();
    setCallInProgress(true);
    try {
      const cognitoUser = await Auth.signIn(data.email, data.password);
      if (cognitoUser?.challengeName === "NEW_PASSWORD_REQUIRED") {
        await Auth.completeNewPassword(cognitoUser, data.password);
      }
      setCallInProgress(false);
      query.invalidateQueries([reactQueryKeys.cognitoUser]);
    } catch (error: any) {
      const errorMessage = "Wrong Email Address or Password";
      setError("root", {
        type: error.code,
        message: errorMessage,
      });
      setCallInProgress(false);
    }
  };

  return (
    <div className={styles.loginPage}>
      {callInProgress ? (
       <div className={styles.loaderImg}><Loader /></div>
      ) : (
        <form onSubmit={handleSubmit(loginFormSubmitHandler)}>
          <div className={styles.inputFiledLoginPass}>
            <div className={styles.emailText}>Email:</div>
            <InputField
              className={styles.InputFieldcss}
              control={control}
              fieldName={register("email").name}
              type="email"
              placeholder="Enter Email Address"
            />
          </div>
          <div className={styles.inputFiledLoginPass}>
            <div className={styles.emailText}>Password:</div>
            <InputField
              className={`${styles.InputFieldcss} ${styles.pass}`}
              control={control}
              fieldName={register("password").name}
              type={showPassword ? "text" :"password"}
              autoComplete="new-password"
              placeholder="Enter Password"
            />
            <button type="button" className={styles.showHidePass} onClick={()=>setShowPassword(x=> !x)}>
              {showPassword ? <HidePass /> : <ShowPass />}
            </button>
          </div>

          <button className={styles.loginBtn} type="submit">
            Login
          </button>
        </form>
      )}
      <span className={styles.wrongEmail}>{errors?.root?.message}</span>
    </div>
  );
};

export default Login;
