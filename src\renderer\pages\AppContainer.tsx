import axios, { AxiosResponse } from "axios";
import { useEffect, useState, createContext } from "react";
import { matchPath, Outlet, useLocation, useNavigate } from "react-router-dom";
import Loader from "../components/common/Loader";
import MatPopup from "../components/common/MatPopup";
import useCognitoUser from "../hooks/useCognitoUser";
import {
  BRYZOS_SEND_INVOICE_GROUP,
  ErrorMessageResponses,
  ROLE_BRYZOS_ADMIN,
  routePaths,
} from "../utils/constant";
import styles from "./AppContainer.module.scss";
import { CognitoUserSession } from "amazon-cognito-identity-js";
import { useImmer } from "use-immer";
import { Logout } from "../utils/helper";
import { commomKeys, createSocket, generateHashFromEncryptedData, setEnvironment, snackbarSeverityType, useGetSecurityData } from '@bryzos/giss-ui-library'; 
import { Auth } from "aws-amplify";
import DialogBox from "../components/common/DialogPopup/Dialog";

export const UserRoleCtx = createContext<string | null>(null);
export const CommonCtx = createContext<any>(null);

const AppContainer = () => {
  const [showPopup, setShowPopup] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [isSecurityHashSet, setIsSecurityHashSet] = useState(false);
  const [userRole, setUserRole] = useImmer<string | null>(null);
  const [socket, setSocket] = useState(null);

  useEffect(() => {
    setEnvironment(import.meta.env);
    axios.interceptors.response.use(
      (response: AxiosResponse) => {
        if (response.status === 200 && response.data?.data === 'Success') {
          setErrorMessage('Security Token Changed');
          setShowPopup(true);
          throw new Error('Something went wrong, Please try again later.');
        }

        if (response.data?.data) {
          if (response.data.data.error_message && !response.data.data.error_redirect) {
            if (!checkToNotShowErrors(response.data.data.error_message)) {
              setErrorMessage(response.data.data.error_message);
              setShowPopup(true);
            }
          } else if (response.data.data.err_message) {
            if (!checkToNotShowErrors(response.data.data.err_message)) {
              setErrorMessage(response.data.data.err_message);
              setShowPopup(true);
            }
          }
        }
        return response;
      },
      (error: any) => {
        if (error.response) {
          if (
            typeof error.response?.data?.data === "object" &&
            "error_message" in error.response.data.data
            ) {
            setErrorMessage(error.response.data.data.error_message);
            setShowPopup(true);
          } else if (error.response?.statusText) {
            setErrorMessage(error.response.statusText);
            setShowPopup(true);
          }
        }

        return Promise.reject(error);
      }
    );
  }, [])

  const showPopupFormAnyComponent = (msg: string) => {
    setErrorMessage(msg);
    setShowPopup(true);
  };

  const checkToNotShowErrors = (errorMessage: string) => {
    return ErrorMessageResponses.notShowErrors.find(
      (obj) => obj === errorMessage
    );
  };

  const location = useLocation();
  const navigate = useNavigate();

  const popupCloseHandler = () => {
    setShowPopup(false);
    ErrorMessageResponses.notAuthorizedException.forEach((error) => {
      if (errorMessage === error) {
        Logout();
      }
    });
    ErrorMessageResponses.refreshOnError.forEach((error) => {
      if (errorMessage === error) {
        window.location.reload();
      }
    })
  };

  const {
    data: cognitoUser,
    isLoading: isCognitoUserLoading,
    error: cognitoUserError,
  } = useCognitoUser();

  const {
    data: getSecurityData,
  } = useGetSecurityData(cognitoUser, import.meta.env.VITE_API_SERVICE + '/reference-data/getSecurityToken');

  const isLoginPage = matchPath(
    {
      path: routePaths.login,
      caseSensitive: true,
    },
    location.pathname
  );
  const isRootPage = matchPath(
    {
      path: "",
      caseSensitive: true,
    },
    location.pathname
  );

  useEffect(() => {
    if (!isCognitoUserLoading) {
      if (cognitoUser) {
        cognitoUser.getSession(
          (error: Error | null, session: CognitoUserSession | null) => {
            if (!error && getSecurityData) {
              initSession(session)
            }
          }
        );
      } else {
        navigate(`/${routePaths.login}`);
        setIsSecurityHashSet(true)
      }
    }
  }, [cognitoUser, cognitoUserError, isCognitoUserLoading, getSecurityData]);

  const socketConnectionErrorHandler = (message) => {
    //right now do nothing
  }
  const removeSocketDisconnectToaster = ()=>{
  }

  const onSocketConnectionError = (errorMsg)=>{
    //right now do nothing
  }

  const initSession = async (session: CognitoUserSession | null) => {
    await configureAxiosInterceptor();
    if (session) {
      let adminlogin =
        session.getAccessToken().payload["cognito:groups"];
      if (adminlogin?.indexOf(ROLE_BRYZOS_ADMIN) > -1) {
        setUserRole(ROLE_BRYZOS_ADMIN);
        //create sockets
        const socketProps = {
          userRole: ROLE_BRYZOS_ADMIN,
          email_id: session?.getIdToken().payload.email,
          socketConnectionErrorHandler: socketConnectionErrorHandler,
          removeSocketDisconnectToaster:  removeSocketDisconnectToaster,
          onSocketConnectionError: onSocketConnectionError,
        }
        const extraHeaders= {
          "gissToken": import.meta.env.VITE_WEB_SOCKET_GISS_TOKEN,
          "email": session?.getIdToken().payload.email,
          "accessToken":  session?.getAccessToken().getJwtToken(),
        };
        const newSocket = await createSocket(import.meta.env.VITE_WEB_SOCKET_SERVER,extraHeaders, socketProps);
        setSocket(newSocket);

        if (isLoginPage || isRootPage) {
          navigate(`/${routePaths.user}/${routePaths.create}`);
        }
      } else if (adminlogin?.indexOf(BRYZOS_SEND_INVOICE_GROUP) > -1) {
        setUserRole(BRYZOS_SEND_INVOICE_GROUP);
        navigate(`/${routePaths.sendInvoiceEmail}`);
      } else {
        setUserRole(null);
        setErrorMessage("This user is not an admin");
        setShowPopup(true);
        return;
      }
    }
  }


  const configureAxiosInterceptor = async () => {
    const secuirtyHashData = await generateHashFromEncryptedData(getSecurityData, "R_=a!<+3;zrx2~?u!6N&&nQ0'aGGkv>IB,C]");
    axios.interceptors.request.clear();
    axios.interceptors.request.use(async (request) => {
      const user = await Auth.currentSession();
      const accessToken = user.getAccessToken();
      const jwt = accessToken.getJwtToken();
      
      if (request.headers.skipInterceptor !== "true" && !request.url?.includes(import.meta.env.VITE_VIDEO_LIBRARY_CLOUD_FRONT)) {
        request.headers.AccessToken = jwt;
      }
      if(!request.url?.includes(import.meta.env.VITE_VIDEO_LIBRARY_CLOUD_FRONT)){
        request.headers.security = secuirtyHashData;
      }
      return request;
    });
    setIsSecurityHashSet(true);
  }

  return (
    <>
      {isCognitoUserLoading ||
      !isSecurityHashSet ? (
        <div className={styles.loaderImg}>
          <Loader />
        </div>
      ) : (
        <UserRoleCtx.Provider value={userRole}>
          <CommonCtx.Provider value={showPopupFormAnyComponent}>
            <Outlet />

            <MatPopup
              className={styles.qualoficationPopup}
              open={showPopup}
              classes={{
                root: styles.orderSuccessPopup,
                paper: styles.orderSuccessPopup1,
              }}
            >
              <div className={styles.qualofication}>
                <p className={styles.qualoficationText}>{errorMessage}</p>
                <button className={styles.okBtn} onClick={popupCloseHandler}>
                  Ok
                </button>
              </div>
            </MatPopup>
          </CommonCtx.Provider>
        </UserRoleCtx.Provider>
      )}
      <DialogBox/>
    </>
  );
};

export default AppContainer;
