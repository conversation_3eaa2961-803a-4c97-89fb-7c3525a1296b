import { useContext, useEffect, useState } from "react";
import { useFieldArray, useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";

import MatPopup from "../../../components/common/MatPopup";
import EditLine from "./EditLine/EditLine";
import useUpdateOrderLine from "../../../hooks/useUpdateOrderLine";
import styles from '../UpdateQtySendInvoiceEmail.module.scss'
import { CommonCtx } from "../../AppContainer";
import useAddNewOrderLines from "../../../hooks/useAddNewOrderLines";
import { ReferenceDataProduct, formatToTwoDecimalPlaces, priceUnits } from "@bryzos/giss-ui-library";
import { PAYMENT_METHOD_ACH_CREDIT } from "../../../utils/constant";
import clsx from "clsx";
import axios from "axios";
import { calculateTotalWeightForProduct, getProductMapping, updatedAllProductsData } from "../../../utils/helper";
import useGetUserSpreadRate from "../../../hooks/useGetUserSpreadRate";
import usePostFetchProductsPrices from "../../../hooks/usePostFetchProductsPrices";
import { useDebouncedValue } from "@mantine/hooks";
import usePostFetchProductsPricesFromPo from "../../../hooks/usePostFetchProductsPricesFromPo";

const Schema = yup.object().shape({
    orderLines: yup.array().of(
        yup.object().shape({
            product: yup.object().required().nullable(),
            domesticMaterialOnly: yup.boolean().oneOf([true, false]).required().nullable(),
            quantity: yup.number().transform((value) => (isNaN(value) ? undefined : value)).required().nullable(),
            qtyUnit: yup.string().required().nullable(),
            buyerPrice: yup.number().transform((value) => (isNaN(value) ? undefined : value)).required().nullable(),
            sellerPrice: yup.number().transform((value) => (isNaN(value) ? undefined : value)).required().nullable(),
            priceUnit: yup.string().required().nullable(),
            weight: yup.number().transform((value) => (isNaN(value) ? undefined : value)).required().nullable(),
            buyerExtended: yup.number().transform((value) => (isNaN(value) ? undefined : value)).required().nullable(),
            sellerExtended: yup.number().transform((value) => (isNaN(value) ? undefined : value)).required().nullable(),
            seller_calculation_price_per_unit: yup.number().nullable(),
            buyer_calculation_price_per_unit: yup.number().nullable(),            
        })
    ).test("validate-order-lines", "Please enter valid value", (values) => {
        if (!values?.length) {
            return false;
        }
        let result = true;

        values.forEach(value => {
            if (Number(value.buyerExtended) <= 0 || !value.priceUnit || !value.product || !value.qtyUnit || Number(value.quantity) <= 0 || Number(value.sellerExtended) <= 0 || Number(value.weight) <= 0) {
                result = false;
                return;
            }
        });
        return result;
    }),
});
export type SchemaType = yup.InferType<typeof Schema>;

export type Props = {
    open: boolean;
    onClose: () => void;
    orderData: any;
    setShowLoader: (value: boolean) => void;
    referenceProducts: any;
    isEdit: boolean
};

const defaultValuesForAddLine = {
    product: null,
    domesticMaterialOnly: false,
    quantity: null,
    qtyUnit: null,
    priceUnit: null,
    weight: null,
    buyerExtended: null,
    sellerExtended: null,
    buyerPrice: null,
    sellerPrice: null,
    seller_calculation_price_per_unit:0,
    buyer_calculation_price_per_unit:0,
};
const powerTo4 = Math.pow(10, 4);

const EditLinesPopup: React.FC<Props> = ({ open, onClose, orderData, setShowLoader, referenceProducts, isEdit }) => {
    const { mutateAsync: updateOrderLine } = useUpdateOrderLine();
    const { data: userSpreadRate , isLoading: isUserSpreadRateLoading } = useGetUserSpreadRate(orderData?.buyer_id);
    const showPopupFormAnyComponent = useContext(CommonCtx);
    const { control, watch, register, setValue, handleSubmit, formState: { errors, isValid } } = useForm<SchemaType>({ resolver: yupResolver(Schema) });
    const { fields, replace, append, remove } = useFieldArray({ control, name: "orderLines" });
    const { mutateAsync: addNewOrderLines } = useAddNewOrderLines();
    const [isDirty, setIsDirty] = useState(false);
    const [buyerExtTotal, setBuyerExtTotal] = useState(0);
    const [sellerExtTotal, setSellerExtTotal] = useState(0);
    const [deposit, setDeposit] = useState(0);
    const [salesTax, setSalesTax] = useState(0);
    const [totalPurchase, setTotalPurchase] = useState(0);
    const [salesTaxCounter, setSalesTaxCounter] = useState(0);
    const [isDisable, setIsDisable] = useState<boolean>(false);
    const [isProductNull, setIsProductNull] = useState<boolean>(false);
    const [productData,setProductData] = useState<any>(referenceProducts);
    const [productMapping, setProductMapping] = useState<any>(null);
    const [productPriceMapping, setProductPriceMapping] = useState<any>(null);
    const [debouncedBuyerExtTotal] = useDebouncedValue(buyerExtTotal, 500);
    const [debouncedSellerExtTotal] = useDebouncedValue(sellerExtTotal, 500);
    const [totalWeight, setTotalWeight] = useState(0);
    const [debouncedTotalWeight] = useDebouncedValue(totalWeight, 500);
    const [isProductMappingSet, setIsProductMappingSet] = useState<boolean>(false);


    const { mutateAsync: postFetchProductsPrices } = usePostFetchProductsPrices();
    const { mutateAsync: postFetchProductsPricesFromPo } = usePostFetchProductsPricesFromPo(true);

    useEffect(() => {
        if(referenceProducts && userSpreadRate?.data){
            const getAllProducts = updatedAllProductsData(referenceProducts);
            const productMappingData = getProductMapping(getAllProducts, userSpreadRate?.data);
            setProductData(getAllProducts);
            setProductMapping(productMappingData);
        }
    },[referenceProducts , userSpreadRate?.data])

    useEffect(() => {
        (async()=>{
            if (productData && orderData && productMapping) {
                await setDefaultValues();
            }
            if(!isEdit)
            handleAddNewLine();
        })()
    }, [orderData, productData, productMapping]);

    useEffect(()=>{
        if(isEdit && watch('orderLines')){
            const orders = watch('orderLines') ?? [];
            // Calculate total weight from order lines weight data
            const currentTotalWeight = orders.reduce((total: number, order: any) => {
                return total + (order.weight || 0);
            }, 0);
            setTotalWeight(currentTotalWeight);
        }
    },[watch('orderLines')?.map((order: any) => order?.weight)]);

    const calculateOrderTotalPurchase = async () => {
        const orders = watch('orderLines') ?? [];
        if(orders.length > 0){
            let buyerExt = 0;
            let sellerExt = 0; 
            orders.forEach((order, index) => {
                if(!orderData.purchase_order_lines[index]?.is_canceled){
                    buyerExt += getFloorMultipleOfTwo(order.buyerExtended);
                    sellerExt += getFloorMultipleOfTwo(order.sellerExtended);
                }
            })
            buyerExt = buyerExt / powerTo4;
            sellerExt = sellerExt / powerTo4;
            setBuyerExtTotal(buyerExt);
            setSellerExtTotal(sellerExt);
        }
    }

    useEffect(() => {
        if (isDirty) {
            const calculateTotalPurchase = async () => {
                const salesTax = await calculateSalesTax(debouncedBuyerExtTotal) ?? 0;
                setTotalPurchase((getFloorMultipleOfTwo(debouncedBuyerExtTotal) + getFloorMultipleOfTwo(salesTax) + getFloorMultipleOfTwo(deposit)) / powerTo4);
            }
            calculateTotalPurchase();
        }
    }, [debouncedBuyerExtTotal]);


    const calculateSalesTax = async (matTotal: number) => {
        if (!matTotal) {
            setSalesTax(parseFloat('0'));
            return;
        }
        const taxCounter = salesTaxCounter + 1;
        setSalesTaxCounter(taxCounter)
        const orders = watch('orderLines');
        const payloadData:any = {
            cart_items: [],
            freight_term: "Delivered",
            shipping_details: orderData.shipping_details,
            price: matTotal.toString(),
            salesTaxCounter: salesTaxCounter + 1,
            buyer_id: orderData.buyer_id
        };
        orders?.forEach((order:any, index: number) =>{
            if(!orderData.purchase_order_lines[index]?.is_canceled){
                const item:any = {
                    extended : order.buyerExtended??0,
                };
                payloadData.cart_items.push(item);
            }
        })
        const payload = {
            data: payloadData,
        };
        const salesResponse = await axios.post(`${import.meta.env.VITE_API_ADMIN_SERVICE_NODE}/order/salesTaxCalculate`, payload);
        const salesTaxCounterResponse = salesResponse.data.data.salesTaxCounter;
        if (salesTaxCounterResponse === taxCounter && buyerExtTotal) {
          setSalesTax(parseFloat(salesResponse.data.data.tax));
          return salesResponse.data.data.tax;
        }
    }

    const setDefaultValues = async () => {
        const tax = +orderData.sales_tax;
        const deposit = orderData.deposit_amount ? +orderData.deposit_amount : 0;
        const ext = +orderData.buyer_po_price;
        setSalesTax(tax);
        if(orderData.payment_method === PAYMENT_METHOD_ACH_CREDIT)
        setDeposit(deposit);
        setBuyerExtTotal(ext);
        setSellerExtTotal(+orderData.seller_po_price);
        setTotalPurchase(tax+deposit+ext);
        const orderLinesValue = orderData?.purchase_order_lines?.map((orderLine: any) => {
            let _product = productData.find((product: any) => product.Product_ID === orderLine.product_id);
            const qtyUnit = (orderLine.qty_unit.toLowerCase() === priceUnits.ea) ? priceUnits.pc : orderLine.qty_unit.toLowerCase();
            const priceUnit = (orderLine.price_unit.toLowerCase() === priceUnits.ea) ? priceUnits.pc : orderLine.price_unit.toLowerCase();
            if(orderLine.description || !_product?.UI_Description) _product.UI_Description = orderLine.description;
            if(!_product){
                _product = {
                    domestic_material_only: !!orderLine.domestic_material_only,
                    QUM_Dropdown_Options: qtyUnit,
                    PUM_Dropdown_Options: priceUnit,
                    available: false,
                    id: orderLine.reference_product_id,
                    Product_ID: orderLine.product_id,
                    Key2: orderLine.shape,
                }
                orderLine.buyer_calculation_price_per_unit = orderLine.buyer_calculation_price_per_unit ?? 1;
                orderLine.seller_calculation_price_per_unit = orderLine.seller_calculation_price_per_unit ?? 1;
            }
            const precision = priceUnit?.toLowerCase() === priceUnits.lb ? 4 : 2;
            
            const isCancelled = orderLine?.is_canceled;
            const isBuyerClosed = orderLine?.is_buyer_order_open === 0 ? true: false;
            const isSellerClosed = orderLine?.is_seller_order_open === 0 ? true: false;
            return {
                product: _product,
                quantity: +orderLine.qty,
                qtyUnit,
                buyerPrice: +Number(orderLine.buyer_price).toFixed(precision),
                sellerPrice: +Number(orderLine.seller_price).toFixed(precision),
                priceUnit,
                weight: +orderLine.total_weight,
                buyerExtended: +orderLine.buyer_extended,
                sellerExtended: +orderLine.seller_extended,
                domesticMaterialOnly: !!orderLine.domestic_material_only,
                buyer_calculation_price_per_unit: orderLine.buyer_calculation_price_per_unit,
                seller_calculation_price_per_unit: orderLine.seller_calculation_price_per_unit,
                isCancelled,
                isBuyerClosed,
                isSellerClosed
            };
        });
        const orderLinesWithPrices = await getOrderLinesPrices(orderLinesValue);
        setTimeout(() => {
            setIsProductMappingSet(true);
        }, 500);
        replace(orderLinesWithPrices);
    }

    const getOrderLinesPrices = async (orderLines: any) => {
        let totalWeight = calculateTotalWeightForProduct(orderLines);
        const cartItemsList = orderLines?.map((order: any) => order.product?.Product_ID) || [];
        const productPriceMapping = await getProductPriceObj(cartItemsList, totalWeight);
        orderLines.forEach((order: any, index: number) => {
            if(order?.product) order.product.priceObj = productPriceMapping[order.product.Product_ID]?.prices;
        });
        return orderLines;
    }

    const handleFormSumbit = async (formData: any) => {
        let cartItems = formData.orderLines.reduce((acc: any[], lineData: any, index: number) => {
            if(isEdit || index >= orderData.purchase_order_lines.length){
                const data = orderData.purchase_order_lines[index];
                const product:any = lineData.product;
                acc.push({
                    purchase_order_line_id: data?.id,
                    line_id: index + 1,
                    product_id: product.Product_ID,
                    reference_product_id: product.id,
                    qty_unit: lineData.qtyUnit.toUpperCase(),
                    buyer_price: lineData.buyerPrice,
                    seller_price: lineData.sellerPrice,
                    price_unit: lineData.priceUnit.toUpperCase(),
                    qty: lineData.quantity,
                    total_weight: lineData.weight,
                    buyer_extended: lineData.buyerExtended,
                    seller_extended: lineData.sellerExtended,
                    domestic_material_only: lineData.domesticMaterialOnly,
                    seller_calculation_price_per_unit: lineData.seller_calculation_price_per_unit,
                    buyer_calculation_price_per_unit: lineData.buyer_calculation_price_per_unit,
                    shape: product.Key2,
                    description: product.UI_Description
                })
            }
            return acc;
        },[])
        setShowLoader(true);
        onClose();
        try {
            const payload = { "purchase_order_id": orderData.id, "cart_items": cartItems };
            const result = isEdit ? await updateOrderLine(payload): await addNewOrderLines(payload);
            showPopupFormAnyComponent(result);
            setShowLoader(false);
        } catch (err) {
            setShowLoader(false);
        }
    };

    const handleAddNewLine = () => {
        const orders = watch('orderLines');
        if(orders)
        append({...defaultValuesForAddLine });
    }

    const handleDeleteLine = (index: number) => {
        remove(index)
        if (fields.length === orderData.purchase_order_lines.length + 1) {
            append({...defaultValuesForAddLine});
        }
        calculateOrderTotalPurchase();
    }

    const onReset = () => {
        setIsProductMappingSet(false);
        setDefaultValues();
        setIsDirty(false);
    }

    const getFloorMultipleOfTwo = (value: number | null | undefined): number => {
        if(value){
            const precisionMultiple = value * powerTo4;
            const floorValue = Math.floor(precisionMultiple);
            return floorValue;
        }
        else return 0;
    }

    const getProductPriceObj = async (productIdList: any, totalWeight: any) => {
        const buyerSpreadRate = userSpreadRate?.data?.buyer_spread_rate;
        const sellerSpreadRate = userSpreadRate?.data?.seller_spread_rate;
        let productPriceObj: Record<string, any> = {};
        console.log(orderData, "orderData.buyer_zip_code")
        const productPriceMapping = await postFetchProductsPricesFromPo({
                "po_number": orderData.buyer_po_number,
                "zip_code": orderData.shipping_details.zip,
                "order_size": totalWeight, // can be string|or number but string should also be numeric 
                "product_id": productIdList
        });
        productPriceObj = {...productPriceMapping};
        Object.keys(productPriceMapping).forEach((key: string) => {
            let buyerPrice: Record<string, number> = {};
            let sellerPrice: Record<string, number> = {};
            Object.keys(productPriceMapping[key]).forEach((priceKey: string) => {
                buyerPrice[priceKey] = productPriceMapping[key][priceKey];
                sellerPrice[priceKey] = productPriceMapping[key][priceKey];
                if(userSpreadRate?.data?.is_buyer_spread){
                    buyerPrice[priceKey] = buyerPrice[priceKey] * parseFloat(buyerSpreadRate);
                    sellerPrice[priceKey] = sellerPrice[priceKey] * parseFloat(sellerSpreadRate);
                }
            });
            productPriceObj[key].prices = {
                buyerPrice,
                sellerPrice
            };
        });
        if(Object.keys(productPriceObj).length > 1){
            setProductPriceMapping(productPriceObj);
        }else{
            setProductPriceMapping(null);
        }
        return productPriceObj;
    }

    return (
        <MatPopup open={open}
            classes={{
                root: styles.editLinePopup,
                paper: styles.editLineContent
            }}
        >
            <button className={styles.crossBtn} onClick={onClose}>X</button>
            <div className={styles.headerDialog}>{isEdit ? "Edit Lines" : "Add Lines"}  <span className={styles.headerDetail}>({orderData.buyer_internal_po}: {orderData.buyer_po_number})</span></div>
            {
                (isUserSpreadRateLoading || !userSpreadRate?.data) ? <div className={clsx(styles.loadingLinetxt, styles.blink)}>Loading Lines...</div> :
                <>
            <div className={clsx(styles.tblscroll,styles.editLinestbl)}>
                <table>
                    <thead>
                        <tr>
                            <th></th>
                            <th>LN</th>
                            <th></th>
                            <th>Product</th>
                            <th>Domestic Only</th>
                            <th>Quantity</th>
                            <th>Qty Unit</th>
                            <th>Seller Price</th>
                            <th>Buyer Price</th>
                            <th>Price Unit</th>
                            <th>Weight</th>
                            <th>Seller Extended</th>
                            <th>Buyer Extended</th>
                            {!isEdit && <th></th>}
                        </tr>
                    </thead>
                    <tbody>
                        {fields?.map((field: any, index: number) => (
                            <EditLine 
                            key={field.id} 
                            index={index} 
                            watch={watch} 
                            setValue={setValue} 
                            register={register} 
                            productData={productData} 
                            control={control} 
                            errors={errors} 
                            isEdit={isEdit} 
                            initialLength={orderData.purchase_order_lines.length} 
                            isCancelled={orderData.purchase_order_lines[index]?.is_canceled}
                            isBuyerClosed={orderData.purchase_order_lines[index]?.is_buyer_order_open === 0 ? true: false}
                            isSellerClosed={orderData.purchase_order_lines[index]?.is_seller_order_open === 0 ? true: false}
                            removeLineItem={handleDeleteLine} setIsDirty={setIsDirty}
                            calculateOrderTotalPurchase={calculateOrderTotalPurchase}
                            isDirty={isDirty}
                            setIsDisable= {setIsDisable}
                            setIsProductNull = {setIsProductNull}
                            isProductAvailable = {field.product?.available ?? true}
                            getProductPriceObj={getProductPriceObj}
                            debouncedBuyerExtTotal={debouncedBuyerExtTotal}
                            debouncedSellerExtTotal={debouncedSellerExtTotal}
                            productPriceMapping={productPriceMapping}
                            debouncedTotalWeight={debouncedTotalWeight}
                            isProductMappingSet={isProductMappingSet}
                            />
                        ))}
                    </tbody>
                </table>
               {fields.length === 0 && <div className={clsx(styles.loadingLinetxt, styles.blink)}>Loading Lines...</div>}
            </div>
            {fields.length !== 0 && <div className={styles.orderTotalPricing}>
                <table className={clsx(styles.ordersTotal,styles.ordersTotalBuyer)}>
                    <tr className={styles.ordersTotalRow}>
                        <td className={styles.ordersTotalTitle}>Buyer Ext. Total</td>
                        <td className={styles.ordersTotalDOllarSign}>$</td>
                        <td className={styles.ordersTotalValue}>{formatToTwoDecimalPlaces(buyerExtTotal.toString())}</td>
                    </tr>
                    <tr className={styles.ordersTotalRow}>
                        <td className={styles.ordersTotalTitle}>Sales Tax</td>
                        <td className={styles.ordersTotalDOllarSign}>$</td>
                        <td className={styles.ordersTotalValue}>{formatToTwoDecimalPlaces(salesTax.toString())}</td>
                    </tr>
                    {orderData.payment_method === PAYMENT_METHOD_ACH_CREDIT && <tr className={styles.ordersTotalRow}>
                        <td className={styles.ordersTotalTitle}>Deposit</td>
                        <td className={styles.ordersTotalDOllarSign}>$</td>
                        <td className={styles.ordersTotalValue}>{formatToTwoDecimalPlaces(deposit.toString())}</td>
                    </tr>}
                    <tr className={clsx(styles.ordersTotalRow, styles.boldFont)}>
                        <td className={styles.ordersTotalTitle}>Total Purchase</td>
                        <td className={styles.ordersTotalDOllarSign}>$</td>
                        <td className={styles.ordersTotalValue}>{formatToTwoDecimalPlaces(totalPurchase.toString())}</td>
                    </tr>
                </table>
                <table className={clsx(styles.ordersTotal,styles.ordersTotalSeller)}>
                    <tr className={clsx(styles.ordersTotalRow, styles.verticalTop)}>
                            <td className={styles.ordersTotalTitle}>Seller Ext. Total</td>
                            <td className={styles.ordersTotalDOllarSign}>$</td>
                            <td className={styles.ordersTotalValue}>{formatToTwoDecimalPlaces(sellerExtTotal.toString())}</td>
                    </tr>
                </table>
                <div className={styles.addBtn}>
                    {!isEdit && <button className={styles.sendEmailBtn} onClick={handleAddNewLine}>Add Line</button>}
                </div>
            </div>}
            <div className={styles.btnSection}>
                    {isEdit && <button className={styles.sendEmailBtn} onClick={onReset} disabled={!isDirty}>Reset</button>}
                    <button className={styles.sendEmailBtn} disabled={!isValid || !isDirty || isDisable || isProductNull} onClick={handleSubmit(handleFormSumbit)}>Save</button>
                    <button className={styles.sendEmailBtn} onClick={onClose}>Cancel</button>
            </div>               
            </>
            }

        </MatPopup>
    );
};

export default EditLinesPopup;
