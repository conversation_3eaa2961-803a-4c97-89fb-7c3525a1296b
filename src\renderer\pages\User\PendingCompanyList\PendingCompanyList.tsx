import { Select, MenuItem, Tooltip, Autocomplete, TextField } from "@mui/material";
import { useEffect, useState } from "react";
import ReactPaginate from "react-paginate";
import { useImmer } from "use-immer";
import Loader from "../../../components/common/Loader/Loader";
import { filterArrray } from "../../../utils/helper";
import styles from "./PendingCompanyList.module.scss";
import MatPopup from "../../../components/common/MatPopup";
import { CreateUserFromSchema, CreateUserFromSchemaType } from "../../../models/createUser.model";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import useGetAllPendingOnBoardCompanies from "../../../hooks/useGetAllPendingOnBoardCompanies";
import usePostIsApproveCompany, { ApproveCompanyPayload } from "../../../hooks/usePostIsApproveCompany";
import usePostAddCompany from "../../../hooks/usePostAddCompany";
import usePostEditCompany from "../../../hooks/usePostEditCompany";
import useGetAllCompany from "../../../hooks/useGetAllCompany";
import clsx from "clsx";
import DiscountPopup from "../../../components/DiscountPopup";
import dayjs from "dayjs";
import usePostUpdateCompanyDiscount from "../../../hooks/usePostUpdateCompanyDiscount";
import useGetAdminReferenceData from "../../../hooks/useGetAdminReferenceData";
import Map from "../../Map";
import useGetCompanyResaleCertificate from "../../../hooks/useGetCompanyResaleCertificate";
import usePostCompanyResaleCertUpload from "../../../hooks/usePostCompanyResaleCertUpload";
import useGetReferenceData from "../../../hooks/useGetReferenceData";
import usePostDeleteCompanyResaleCert from "../../../hooks/usePostDeleteCompanyResaleCert";
const PendingCompanyList = () => {
  const [filteredaPendingCompanies, setFilteredaPendingCompanies] = useImmer<any>([]);
  const [allCompanies, setAllCompanies] = useImmer<any>([]);

  const [inputSearchValue, setInputSearchValue] = useState("");
  const [itemOffset, setItemOffset] = useState(0);
  const [perPageEntriesOptions] = useState([10, 25, 50, 100]);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(0);
  const [callInProgress, setCallInProgress] = useState(false);
  const [showConfirmationPopup, setShowConfirmationPopup] = useImmer(false);
  const [showNewUserPopup, setShowNewUserPopup] = useImmer(false);
  const [apiResponseMessage, setApiResponseMessage] = useImmer("");
  const [formData, setFormdata] = useImmer<CreateUserFromSchemaType | null>(
    null
  );
  const [userId, setuserId] = useImmer("");
  const [approveAction, setApproveAction] = useImmer<boolean>(false);
  const [showAddCompanyPopup, setShowAddCompanyPopup] = useImmer(false);
  const [addCompanyName, setAddCompanyName] = useImmer("");
  const [editCompanyNameData, setEditCompanyNameData] = useImmer<any>(null);
  const [searchCompanyName, setSearchCompanyName] = useImmer<any>(null);
  const [disabledSubmitCompanyListBtn, setDisabledSubmitCompanyListBtn] = useImmer<any>(true);
  const [allCompanyNameChecked, setAllCompanyNameChecked] = useImmer<any>(false);
  const [pageChecked, setPageChecked] = useImmer<any>(null);
  const [specificCompanyData, setSpecificCompanyData] = useImmer<ApproveCompanyPayload | null>(null);
  const [editedCompanyName, setEditedCompanyName] = useImmer("");
  const [showEditCompanyPopup, setShowEditCompanyPopup] = useImmer(false);
  const [editCompanyDetails, setEditCompanyDetails] = useImmer<any>(null);
  const [showResaleCertPopup, setShowResaleCertPopup] = useState(false);
  const [resaleCertPopupData, setResaleCertPopupData] = useImmer<any>(null);
  const [stateListData, setStateListData] = useImmer<any[]>([]);
  const [deleteCertData, setDeleteCertData] = useState<any>({});
  const [certState, setCertState] = useImmer<any[]>([]);
  const [allowedApiKeyCount,setAllowedApiKeyCount] = useState(0)
  const [sandboxAllowedApiKeyCount,setSandboxAllowedApiKeyCount] = useState(0)


  const endOffset = itemOffset + itemsPerPage;
  const pageCount = Math.ceil(filteredaPendingCompanies.length / itemsPerPage);

  const { data: allCompany, isLoading: allCompanyLoading, isFetching: allCompanyFetching } = useGetAllCompany();
  const { data: allPendingCompanies, isLoading: allPendingCompaniesLoading, isFetching: allPendingCompaniesFetching } = useGetAllPendingOnBoardCompanies();
  const { data: adminReferenceData, isLoading: adminReferenceDataLoading } = useGetAdminReferenceData();
  const { data: companyResaleCertificate, isLoading: companyResaleCertificateLoading, isFetching: companyResaleCertificateFetching }: {data: any[] | undefined, isLoading: boolean, isFetching: boolean} = useGetCompanyResaleCertificate();
  const { data: referenceData, isLoading: isGetReferenceDataLoading } = useGetReferenceData();
  const {
    mutate: isApproveCompany,
    isLoading: isApproveCompanyLoading,
    data: approveCompanyData,
  } = usePostIsApproveCompany();

  const {
    mutate: addCompany,
    isLoading: isAddCompanyLoading,
    data: addCompanyData,
  } = usePostAddCompany();

  const {
    mutate: editCompany,
    isLoading: isEditCompanyLoading,
    data: editCompanyData,
  } = usePostEditCompany();

  const {
    mutate: updateCompanyDiscount,
    isLoading: isUpdateCompanyDiscountLoading,
    data: updateCompanyDiscountData,
  } = usePostUpdateCompanyDiscount();

  const {
    mutate: companyResaleCertUpload,
    isLoading: isCompanyResaleCertUploadLoading,
    data: companyResaleCertUploadData,
  } = usePostCompanyResaleCertUpload();

  const {
    mutate: deleteCompanyResaleCert,
    isLoading: isDeleteCompanyResaleCertLoading,
    data: deleteCompanyResaleCertData,
  } = usePostDeleteCompanyResaleCert();

  const {
    setError,
    reset,
    formState: { errors },
  } = useForm<CreateUserFromSchemaType>({
    resolver: yupResolver(CreateUserFromSchema),
  });

  useEffect(() => {
    if (allPendingCompanies && allCompany && companyResaleCertificate && referenceData) {
      let createPendingCompanies = [];
      createPendingCompanies = allPendingCompanies.map((pendingCompany: any) => {
        if (pendingCompany.isChecked === false) return pendingCompany;
        pendingCompany.isChecked = false;
        return pendingCompany
      })
      
      const _allCompany = JSON.parse(JSON.stringify(allCompany));
      _allCompany.forEach((data, i) => {
        _allCompany[i].certificateData = [];
        companyResaleCertificate.forEach((certData)=>{
          if(+certData.company_id === data.id){
            const refStateData = referenceData.ref_states.find(refData => refData.id === certData.state_id);
            if(refStateData){
              certData.stateCode = refStateData?.code;
              certData.stateName = refStateData?.value;
              _allCompany[i].certificateData.push({...certData});
            }
          }
        })
      });
      
      createPendingCompanies.push(..._allCompany)
      setAllCompanies(createPendingCompanies)
      if (inputSearchValue.length === 0) {
        setFilteredaPendingCompanies(createPendingCompanies);
      }
    } else {
      setFilteredaPendingCompanies([]);
    }
  }, [allPendingCompanies, allCompany, companyResaleCertificate, referenceData]);

  useEffect(()=>{
    if(referenceData?.ref_states){
      setStateListData(referenceData.ref_states);
    }
  },[referenceData?.ref_states])

  useEffect(() => {
    if (allCompanies.length > 0 && inputSearchValue.length !== 0) {
      search(inputSearchValue)
    }
  }, [allCompanies])

  useEffect(() => {
    setCurrentPage(0);
    setItemOffset(0);
    const pageList: any = {};
    if (pageCount !== 0) {
      for (let index = 0; index < pageCount; index++) {
        pageList[index] = false
      }
    } else {
      pageList[pageCount] = false
    }
    setPageChecked(pageList)
  }, [itemsPerPage, pageCount]);

  useEffect(() => {
    if (approveCompanyData && !isApproveCompanyLoading) {
      setApiResponseMessage(approveCompanyData);
    }
  }, [approveCompanyData, isApproveCompanyLoading]);

  useEffect(() => {
    if (addCompanyData && !isAddCompanyLoading) {
      setApiResponseMessage(addCompanyData);
    }
  }, [addCompanyData, isAddCompanyLoading]);

  useEffect(() => {
    if (editCompanyData && !isEditCompanyLoading) {
      setApiResponseMessage(editCompanyData);
    }
  }, [editCompanyData, isEditCompanyLoading]);

  useEffect(() => {
    if (updateCompanyDiscountData && !isUpdateCompanyDiscountLoading) {
      setApiResponseMessage(updateCompanyDiscountData);
    }
  }, [updateCompanyDiscountData, isUpdateCompanyDiscountLoading]);

  useEffect(() => {
    if (companyResaleCertUploadData && !isCompanyResaleCertUploadLoading) {
      const stateList = certState.reduce((acc, item) => {
        if (item) {
          if (acc.length > 0) {
            acc += ", ";
          }
          acc += item.value;
        }
        return acc;
      }, "");
      const companyResaleCertUploadMessage = companyResaleCertUploadData + ' for '+ stateList;
      setApiResponseMessage(companyResaleCertUploadMessage);
    }
  }, [companyResaleCertUploadData, isCompanyResaleCertUploadLoading]);

  useEffect(() => {
    if (deleteCompanyResaleCertData && !isDeleteCompanyResaleCertLoading) {
      const deleteCertMessage = deleteCertData.stateName + ' ' + deleteCompanyResaleCertData;
      setApiResponseMessage(deleteCertMessage);
    }
  }, [deleteCompanyResaleCertData, isDeleteCompanyResaleCertLoading]);

  useEffect(() => {
    if (filteredaPendingCompanies.length !== 0) {
      let pendingCompanies: any[] = [];
      let selectedPendingCompanies: any[] = [];
      filteredaPendingCompanies.forEach((company: any) => {
        if ('isChecked' in company) {
          pendingCompanies.push(company)
        }
      });
      const companyNameIsChecked = pendingCompanies.every((company: any) => company.isChecked === false)
      if (companyNameIsChecked) {
        setDisabledSubmitCompanyListBtn(companyNameIsChecked)
      } else {
        setDisabledSubmitCompanyListBtn(companyNameIsChecked)
      }
      filteredaPendingCompanies.slice(itemOffset, endOffset).forEach((company: any) => {
        if ('isChecked' in company) {
          selectedPendingCompanies.push(company)
        }
      });
      const isFilteredAllCompanySelected = selectedPendingCompanies.length !== 0 ? selectedPendingCompanies.every((company: any) => company.isChecked === true) : null;
      setAllCompanyNameChecked(isFilteredAllCompanySelected)
    }
  }, [filteredaPendingCompanies])

  const search = (searchValue: string) => {
    setCurrentPage(0);
    setItemOffset(0);
    setInputSearchValue(searchValue);
    if (searchValue) {
      const _filterArrray = filterArrray(allCompanies, searchValue, [
        "company_name",
      ]);
      if (_filterArrray?.length) {
        setFilteredaPendingCompanies(_filterArrray);
      } else {
        setFilteredaPendingCompanies([]);
      }
    } else {
      setFilteredaPendingCompanies(allCompanies ? allCompanies : []);
    }
  };

  const handlePageClick = (event: any) => {
    const newOffset = (event.selected * itemsPerPage) % filteredaPendingCompanies.length;
    setCurrentPage(event.selected);
    setItemOffset(newOffset);
    const newEndOffset = newOffset + itemsPerPage;
    const isCheckedAvailable: any = [];
    filteredaPendingCompanies.slice(newOffset, newEndOffset).forEach((company: any) => {
      if ('is_approved' in company) {
        isCheckedAvailable.push(company)
      }
    })
    if (isCheckedAvailable.length !== 0) {
      setAllCompanyNameChecked(pageChecked[event.selected])
    } else {
      setAllCompanyNameChecked(null)
    }
  };

  const confirmationPopupYes = () => {
    confirmationPopupClose();
    let payload: any = { data: [] };
    let selectedCompanies: any[] = [];
    filteredaPendingCompanies.forEach((company: any) => {
      if (company.isChecked) {
        selectedCompanies.push({
          id: company.id,
          company_name: company.company_name,
        })
      }
    });
    payload.data = specificCompanyData ? [{ ...specificCompanyData, company_name: editedCompanyName }] : selectedCompanies;
    isApproveCompany({ data: payload, action: approveAction })
  };

  const confirmationPopupClose = () => {
    setShowConfirmationPopup(false);
    setShowAddCompanyPopup(false);
    setApproveAction(false);
    setAddCompanyName('');
    setEditCompanyNameData(null)
    setSearchCompanyName(null)
    setSpecificCompanyData(null);
    setEditedCompanyName("");
    setShowEditCompanyPopup(false)
  };


  
  const checkboxChangeHandler = (event: any, companyData: any) => {
    const companyNameIndex = filteredaPendingCompanies.findIndex((company: any) => company.id === companyData.id);
    setFilteredaPendingCompanies((prev: any) => {
      prev[companyNameIndex].isChecked = event.target.checked;
      return prev;
    });
  };

  const handleApproveCompanies = (action: boolean, company: ApproveCompanyPayload | null) => {
    if (company) {
      setEditedCompanyName(company.company_name);

      const specificCompany = {
        id: company.id,
        company_name: company.company_name,
      };
      setSpecificCompanyData(specificCompany)
    }
    setApproveAction(action);
    setShowConfirmationPopup(true)
  }

  const handleAddCompany = () => {
    confirmationPopupClose();
    const payload = {
      "data": {
        "company_name": addCompanyName,
      }
    }
    if (editCompanyNameData && addCompanyName ) {
      let payloadData = editCompanyNameData;
      payloadData.data.company_name = addCompanyName
      payloadData.data.allowed_api_keys_count = (allowedApiKeyCount === "")  ?  0 : Number(allowedApiKeyCount)
      payloadData.data.sandbox_allowed_api_keys_count = (sandboxAllowedApiKeyCount === "")  ?  0 : Number(sandboxAllowedApiKeyCount)
      editCompany(payloadData)
    } else {
      addCompany(payload)
    }
  }
  const handleEditCompany = ({id,company_name ,allowed_api_keys_count, sandbox_allowed_api_keys_count}:any) => {
    setAddCompanyName(company_name)
    setAllowedApiKeyCount(allowed_api_keys_count ? allowed_api_keys_count : 0)
    setSandboxAllowedApiKeyCount(sandbox_allowed_api_keys_count ? sandbox_allowed_api_keys_count : 0)
    const payload = {
      "data": {
        "id": id,
        "company_name": null
      }
    }
    setEditCompanyNameData(payload)
    setShowAddCompanyPopup(true)
    setSearchCompanyName(null)
  }
  const handleDiscountCompany = (company: any) => {
    setEditCompanyDetails(company)
    setShowEditCompanyPopup(true);
    setSearchCompanyName(null)
  }

  const allPendingCompanyChecked = (e: any, currentPage: number) => {
    let companyList: any[] = [];
    filteredaPendingCompanies.slice(itemOffset, endOffset).forEach((company: any) => {
      if ('isChecked' in company) {
        companyList.push(company)
      }
    })
    if (e.target.checked === true) {
      companyList.forEach((companyData: any, i: any) => {
        const companyNameIndex = filteredaPendingCompanies.findIndex((company: any) => company.id === companyData.id);
        setFilteredaPendingCompanies((prev: any) => {
          prev[companyNameIndex].isChecked = true;
          return prev;
        });
      })
      setPageChecked((prev: any) => {
        prev[currentPage] = true
        return prev
      })
      setAllCompanyNameChecked(true)
    } else {
      companyList.forEach((companyData: any, i: number) => {
        const companyNameIndex = filteredaPendingCompanies.findIndex((company: any) => company.id === companyData.id);
        setFilteredaPendingCompanies((prev: any) => {
          prev[companyNameIndex].isChecked = false;
          return prev;
        });
      })
      setPageChecked((prev: any) => {
        prev[currentPage] = false
        return prev
      })
      setAllCompanyNameChecked(false)
    }
  }

  const handleDropdownChange = (event: any) => {
    setItemsPerPage(+event.target.value);
    let e = {
      target: {
        check: false
      }
    }
    if (allCompanyNameChecked) {
      e.target.check = true
      allPendingCompanyChecked(e, currentPage)
    } else {
      e.target.check = false
      allPendingCompanyChecked(e, currentPage)
    }
  }

  const handleDisableBtn = () => {
    if (addCompanyName.length === 0 || /<|>/g.test(addCompanyName)) {
      return true
    }
    return false;
  }

  const submitEditCompanyDetails = (data: any) => {
    let payload: any = {
      "company_id": data.id,
    }
    const phaseoutStartDate = data?.discDiscountPhaseoutStartdate ? dayjs(data.discDiscountPhaseoutStartdate).format('YYYY-MM-DD') : null;
    payload.is_discount = data.discIsDiscounted
    payload.discount_percentage = data.discDiscountRate
    payload.discount_period = data.discDiscountPeriod
    payload.discount_phaseout_startdate = phaseoutStartDate
    payload.discount_phaseout_period = data.discDiscountPhaseoutPeriod
    payload.discount_pricing = 'Neutral_Pricing'//data.discDiscountPricingColumn
    payload.seller_spread_percentage = data.spreadRate
    updateCompanyDiscount({data: payload})
    confirmationPopupClose();
  }

  const handleResaleCert = (comapny: any) => {
    setResaleCertPopupData(comapny);
    setShowResaleCertPopup(true)
    

  }

  const closeResaleCertPopup = () => {
    setResaleCertPopupData(null);
    setShowResaleCertPopup(false);
  }

  const uploadCompanyCertificate = (data) => {
    companyResaleCertUpload(data);
    confirmationPopupClose();
  }

  const handleApiResponseBtn = () => {
    setApiResponseMessage("")
    setCertState([]);
    setDeleteCertData({});
  }

  const handleKeyDown = (e) => {
    if (e.key === '.' || e.key === ',') {
      e.preventDefault();
    }
  };

  return (
    <div className="contentMain">
      {
        allCompanyLoading ||
          callInProgress ||
          isAddCompanyLoading ||
          isEditCompanyLoading ||
          isApproveCompanyLoading ||
          allPendingCompaniesLoading ||
          isUpdateCompanyDiscountLoading ||
          adminReferenceDataLoading || 
          allCompanyFetching ||
          allPendingCompaniesFetching ||
          isGetReferenceDataLoading ? (
          <div className="loaderImg">
            <Loader />
          </div>
        ) : (
          <div>
            <div className={styles.searchBox}>
              <Select
                className={styles.showdropdwn}
                value={itemsPerPage}
                onChange={(event) => { handleDropdownChange(event) }}
              >
                {perPageEntriesOptions.map((item, index) => (
                  <MenuItem key={index} value={item}>
                    <span>{item}</span>
                  </MenuItem>
                ))}
              </Select>
              <div className={styles.allCompanyText}>
                <div className={styles.yesAndnoBtnHeader}>
                  <button className={styles.okBtn} onClick={() => handleApproveCompanies(true, null)} disabled={disabledSubmitCompanyListBtn}> Approve Selected </button>
                  <button className={styles.okBtn} onClick={() => handleApproveCompanies(false, null)} disabled={disabledSubmitCompanyListBtn} > Reject Selected  </button>
                </div>
                {/* <Autocomplete
                freeSolo
                  className={clsx(styles.selectDropdown,'companyDropdown')}
                  options={allCompany}
                  value={allCompany?.find((obj: any) => obj.company_name === searchCompanyName) ?? null}
                  getOptionLabel={(option: any) => option.company_name ?? ""}
                  renderInput={(params) => (
                    <TextField {...params} placeholder="Search existing companies to edit" />
                  )}
                  onChange={(event, data: any, reason ) => {
                    setSearchCompanyName(data ? data.value : null)
                    if(reason === "selectOption"){
                      handleEditCompany(data.id,data.company_name)
                    }
                    
                  }}
                  classes={{
                    root: styles.autoCompleteDesc,
                    popper: styles.autocompleteDescPanel,
                    paper: styles.autocompleteDescInnerPanel,
                    listbox: styles.listAutoComletePanel,
                  }}
                /> */}
              </div>
              <button className={styles.addCompanyBtn} onClick={() => setShowAddCompanyPopup(true)} >Add Company</button>
              <input
                className={styles.searchInput}
                type="text"
                onChange={(e) => search(e.target.value)}
                placeholder="Search Company"
                value={inputSearchValue}
              />
            </div>
            <div className={styles.tblscroll}>
              <table>
                <thead>
                  <tr>
                    <th>
                      {allCompanyNameChecked !== null &&
                        <label className="containerChk">
                          <input type="checkbox" onChange={(e) => allPendingCompanyChecked(e, currentPage)} checked={allCompanyNameChecked} />
                          <span className="checkmark" />
                        </label>
                      }
                    </th>
                    <th>Company Name</th>
                    <th>Allowed API Keys Count (Sandbox)</th>
                    <th>Allowed API Keys Count (Prod)</th>
                    <th></th>
                    <th></th>
                    <th></th>
                  </tr>
                </thead>
                <tbody>
                  {filteredaPendingCompanies?.length ? (
                    filteredaPendingCompanies
                      .slice(itemOffset, endOffset)
                      .map((company: any, index: number) => (
                        <tr key={company.id}>
                          <td>
                            {('isChecked' in company) &&
                              <label className="containerChk">
                                <input type="checkbox" checked={company.isChecked} onChange={(e) => checkboxChangeHandler(e, company)} />
                                <span className="checkmark" />
                              </label>
                            }
                          </td>
                          <td>{company.company_name}</td>
                          <td>{company?.sandbox_allowed_api_keys_count ? company.sandbox_allowed_api_keys_count : '-' }</td>
                          <td>{company?.allowed_api_keys_count ? company.allowed_api_keys_count : '-' }</td>
                          {('isChecked' in company) ? (
                            <>
                              <td>
                                <button
                                  className={styles.resetPassBtn}
                                  onClick={() =>
                                    handleApproveCompanies(true, company)
                                  }
                                >
                                  Approve
                                </button>
                              </td>
                              <td></td>
                              <td>
                                <button
                                  className={styles.resetPassBtn}
                                  onClick={() =>
                                    handleApproveCompanies(false, company)
                                  }
                                >
                                  Reject
                                </button>
                              </td>
                            </>
                          ) :
                            (
                              <>
                                <td>
                                  <button
                                    className={styles.resetPassBtn}
                                    onClick={() =>
                                      handleEditCompany(company)
                                    }
                                  >
                                    Edit
                                  </button>
                                </td>
                              <td>
                                <button
                                  className={styles.resetPassBtn}
                                  onClick={() =>
                                    handleResaleCert(company)
                                  }
                                >
                                  Resale Cert
                                </button>
                              </td>
                                <td>
                                  <button
                                    className={styles.resetPassBtn}
                                    onClick={() =>
                                      handleDiscountCompany(company)
                                    }
                                  >
                                    Spread
                                  </button>
                                </td>
                              </>
                            )
                          }
                        </tr>
                      ))
                  ) : (
                    <tr>
                      <td colSpan={4} className={"noDataFoundTd"}>No data found</td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
            <div className={"PaginationNumber"}>
              <ReactPaginate
                breakLabel="..."
                nextLabel=">"
                onPageChange={handlePageClick}
                pageRangeDisplayed={5}
                pageCount={pageCount}
                previousLabel="<"
                renderOnZeroPageCount={(props) =>
                  props.pageCount > 0 ? undefined : null
                }
                forcePage={pageCount > 0 ? currentPage : -1}
              />
            </div>

          </div>
        )}

      <MatPopup
        className={styles.approveRejectPopup}
        open={!!apiResponseMessage}
      >
        <div className={styles.successfullyUpdated}>
          <div className={styles.successfullytext}>{apiResponseMessage}</div>
          <button
            className={styles.okBtn}
            onClick={() => handleApiResponseBtn()}
          >
            Ok
          </button>
        </div>
      </MatPopup>
      <MatPopup
        className={styles.emailNotvalidPopup}
        open={showConfirmationPopup}
      >
        {showConfirmationPopup && <div className={styles.emailNotvalidbox}>
          {specificCompanyData && approveAction ? (
            <p className={styles.emailText}>APPROVE COMPANY NAME</p>
          ) : (
            <p className={styles.emailText}>Do you want to continue ?</p>
          )}
          <div className={styles.yesAndnoBtn}>
            {specificCompanyData && approveAction && (
              <div>
                <p className={styles.CompanyNametext}>( Review the company name below. You can edit before approving or approve as it is. )</p>
                <input
                  className={styles.editCompanyText}
                  type="text"
                  onChange={(e) => setEditedCompanyName(e.target.value)}
                  value={editedCompanyName}
                  placeholder="Enter to edit company name"
                />
              </div>
            )}
            <div className={styles.btnYesno}>
              <button className={styles.okBtn} onClick={confirmationPopupYes} disabled={specificCompanyData ? !editedCompanyName : false}>
                Yes
              </button>
              <button className={styles.okBtn} onClick={confirmationPopupClose}>
                No
              </button>
            </div>
          </div>
        </div>}
      </MatPopup>
      <MatPopup
        className={styles.orderContinuePopup}
        open={showAddCompanyPopup}
      >
        <div className={styles.tblscrollPop}>
          <div className={styles.continuePopup}>
            {editCompanyNameData ?
              <p className={styles.addCompanyTitle}>Edit Company</p> :
              <p className={styles.addCompanyTitle}>Add Company</p>
            }
            <div className={styles.overFlowForPop}>
              <div className={styles.inputDiv}>
                <label>Company Name: </label>
                <input type="text" className={styles.companyNameInput} value={addCompanyName} onChange={(e) => { setAddCompanyName(e.target.value) }} />
              </div>
              {editCompanyNameData && (
                <>
                <div className={styles.inputDiv}>
                  <label>Allowed API Keys Count (Sandbox): </label>
                  <input type="number" className={styles.companyNameInput} min={0} value={sandboxAllowedApiKeyCount} onChange={(e) => {  setSandboxAllowedApiKeyCount(e.target.value) }}   onKeyDown={handleKeyDown} />
                </div>
                  <div className={styles.inputDiv}>
                  <label>Allowed API Keys Count (Prod): </label>
                  <input type="number" className={styles.companyNameInput} min={0} value={allowedApiKeyCount} onKeyDown={handleKeyDown} onChange={(e) => {  setAllowedApiKeyCount(e.target.value) }} />
                  </div>
                </>
              )}
            </div>
            <div className={styles.yesAndnoBtn}>
              <button className={styles.okBtn} onClick={handleAddCompany} disabled={handleDisableBtn()}>
                Save
              </button>
              <button
                className={styles.okBtn}
                onClick={confirmationPopupClose}
              >
                Cancel
              </button>
            </div>
          </div>
        </div>

      </MatPopup>
      <MatPopup
        className={styles.orderContinuePopup}
        open={showEditCompanyPopup}
      >
        <DiscountPopup pageName={"company"} popupHeading={"Edit Spread Detail (Company)"} discountData={editCompanyDetails}  pricingColumnList={adminReferenceData?.pricing_columns} defaultValues={adminReferenceData?.discount_default_values} submitUserDiscount={submitEditCompanyDetails} confirmationPopupClose={confirmationPopupClose} />
        {/* <div className={styles.tblscrollPop}>
          <div className={styles.continuePopup}>
            <p className={styles.addCompanyTitle}>Edit Company</p>
            <div className={styles.overFlowForPop}>
              <input type="text" className={styles.companyNameInput} value={addCompanyName} onChange={(e) => { setAddCompanyName(e.target.value) }} />
            </div>
            <div className={styles.yesAndnoBtn}>
              <button className={styles.okBtn} onClick={handleAddCompany} disabled={handleDisableBtn()}>
                Save
              </button>
              <button
                className={styles.okBtn}
                onClick={confirmationPopupClose}
              >
                Cancel
              </button>
            </div>
          </div>
        </div> */}

      </MatPopup>
      <MatPopup
        classes={{
          root:styles.resaleCertPopup,
          paper:styles.resaleCertPaper

        }}
        open={showResaleCertPopup}
      >
        <Map
          filteredaPendingCompanies={filteredaPendingCompanies}
          companyData={resaleCertPopupData}
          confirmationPopupClose={confirmationPopupClose}
          uploadCompanyCertificate={uploadCompanyCertificate}
          referenceData={referenceData}
          stateData={stateListData}
          setStateListData={setStateListData}
          deleteCompanyResaleCert={deleteCompanyResaleCert}
          closeResaleCertPopup={closeResaleCertPopup}
          isDeleteCompanyResaleCertLoading={isDeleteCompanyResaleCertLoading}
          isCompanyResaleCertUploadLoading={isCompanyResaleCertUploadLoading}
          companyResaleCertificateLoading={companyResaleCertificateLoading}
          companyResaleCertificateFetching={companyResaleCertificateFetching}
          allCompanyLoading={allCompanyLoading}
          allCompanyFetching={allCompanyFetching}
          setDeleteCertData={setDeleteCertData}
          deleteCertData={deleteCertData}
          setCertState={setCertState}
          certState={certState}

        />
      </MatPopup>
    </div>
  );
};

export default PendingCompanyList;
